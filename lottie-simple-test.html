<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Простой тест Lottie</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .animation-box {
            width: 300px;
            height: 300px;
            border: 2px solid #ddd;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #e7f3ff;
            border-left: 4px solid #2196F3;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Простой тест Lottie анимаций</h1>
        
        <div class="status" id="status">
            Загружаем библиотеку Lottie...
        </div>

        <h3>Тест 1: Онлайн анимация (должна работать)</h3>
        <div class="animation-box" id="online-animation">
            <div style="text-align: center; padding-top: 130px; color: #666;">
                Нажмите кнопку для загрузки
            </div>
        </div>
        <button onclick="loadOnlineAnimation()">Загрузить онлайн анимацию</button>

        <h3>Тест 2: Локальная анимация</h3>
        <div class="animation-box" id="local-animation">
            <div style="text-align: center; padding-top: 130px; color: #666;">
                Нажмите кнопку для загрузки
            </div>
        </div>
        <button onclick="loadLocalAnimation()">Загрузить локальную анимацию</button>

        <h3>Консоль:</h3>
        <div id="console" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
    </div>

    <!-- Загружаем Lottie Player -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    
    <script>
        const statusEl = document.getElementById('status');
        const consoleEl = document.getElementById('console');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleEl.textContent += `[${timestamp}] ${message}\n`;
            consoleEl.scrollTop = consoleEl.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, isError = false) {
            statusEl.textContent = message;
            statusEl.style.background = isError ? '#ffebee' : '#e7f3ff';
            statusEl.style.borderLeftColor = isError ? '#f44336' : '#2196F3';
            log(message);
        }

        // Проверяем загрузку библиотеки
        function checkLibrary() {
            if (typeof LottiePlayer !== 'undefined') {
                updateStatus('✅ Библиотека Lottie загружена успешно');
                return true;
            } else {
                updateStatus('❌ Библиотека Lottie не загружена', true);
                return false;
            }
        }

        // Загружаем онлайн анимацию для проверки работоспособности
        function loadOnlineAnimation() {
            log('Загружаем онлайн анимацию...');
            
            if (!checkLibrary()) return;

            const container = document.getElementById('online-animation');
            container.innerHTML = '';

            const player = document.createElement('lottie-player');
            player.setAttribute('src', 'https://assets3.lottiefiles.com/packages/lf20_V9t630.json');
            player.setAttribute('background', 'transparent');
            player.setAttribute('speed', '1');
            player.setAttribute('loop', 'true');
            player.setAttribute('autoplay', 'true');
            player.style.width = '100%';
            player.style.height = '100%';

            player.addEventListener('ready', () => {
                log('✅ Онлайн анимация загружена успешно');
                updateStatus('✅ Онлайн анимация работает - библиотека Lottie функционирует');
            });

            player.addEventListener('error', (e) => {
                log(`❌ Ошибка онлайн анимации: ${e.detail || e.message}`);
                updateStatus('❌ Ошибка загрузки онлайн анимации', true);
            });

            container.appendChild(player);
        }

        // Загружаем локальную анимацию
        function loadLocalAnimation() {
            log('Загружаем локальную анимацию...');
            
            if (!checkLibrary()) return;

            const container = document.getElementById('local-animation');
            container.innerHTML = '';

            const player = document.createElement('lottie-player');
            player.setAttribute('src', 'animations/1.json');
            player.setAttribute('background', 'transparent');
            player.setAttribute('speed', '1');
            player.setAttribute('loop', 'true');
            player.setAttribute('autoplay', 'true');
            player.style.width = '100%';
            player.style.height = '100%';

            player.addEventListener('ready', () => {
                log('✅ Локальная анимация загружена успешно');
                updateStatus('✅ Локальная анимация работает');
            });

            player.addEventListener('error', (e) => {
                log(`❌ Ошибка локальной анимации: ${e.detail || e.message}`);
                updateStatus('❌ Ошибка загрузки локальной анимации - проверьте путь к файлу', true);
            });

            container.appendChild(player);
        }

        // Проверяем библиотеку при загрузке
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkLibrary();
            }, 1000);
        });

        // Обработчик ошибок
        window.addEventListener('error', (e) => {
            log(`❌ JavaScript ошибка: ${e.message}`);
        });
    </script>
</body>
</html>
