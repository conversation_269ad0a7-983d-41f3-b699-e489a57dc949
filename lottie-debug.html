<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Debug - Полная диагностика</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        
        .animation-test {
            width: 200px;
            height: 200px;
            border: 2px solid #ddd;
            margin: 10px;
            display: inline-block;
            background: #f9f9f9;
            position: relative;
        }
        
        .test-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-unknown { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Lottie Debug - Полная диагностика</h1>
        
        <div class="section info">
            <h3>📋 Системная информация</h3>
            <div id="system-info">Загружаем...</div>
        </div>

        <div class="section" id="library-status">
            <h3>📚 Статус библиотеки Lottie</h3>
            <div id="library-info">Проверяем...</div>
        </div>

        <div class="section" id="files-status">
            <h3>📁 Проверка файлов анимаций</h3>
            <div id="files-info">Проверяем...</div>
        </div>

        <div class="section">
            <h3>🎬 Тесты анимаций</h3>
            <div>
                <button onclick="testOnlineAnimation()">Тест онлайн анимации</button>
                <button onclick="testLocalAnimations()">Тест локальных анимаций</button>
                <button onclick="testAllAnimations()">Тест всех анимаций</button>
                <button onclick="clearTests()">Очистить тесты</button>
            </div>
            <div class="test-grid" id="animation-tests"></div>
        </div>

        <div class="section">
            <h3>📝 Лог диагностики</h3>
            <button onclick="clearLog()">Очистить лог</button>
            <div class="log" id="debug-log"></div>
        </div>
    </div>

    <!-- Загружаем Lottie Player -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    
    <script>
        const log = document.getElementById('debug-log');
        const systemInfo = document.getElementById('system-info');
        const libraryInfo = document.getElementById('library-info');
        const filesInfo = document.getElementById('files-info');
        const testsContainer = document.getElementById('animation-tests');

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            log.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            log.scrollTop = log.scrollHeight;
            console.log(`${prefix} ${message}`);
        }

        function clearLog() {
            log.textContent = '';
        }

        function updateSystemInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                protocol: window.location.protocol,
                host: window.location.host || 'file://',
                pathname: window.location.pathname
            };

            systemInfo.innerHTML = `
                <div><strong>Браузер:</strong> ${info.userAgent}</div>
                <div><strong>Платформа:</strong> ${info.platform}</div>
                <div><strong>Язык:</strong> ${info.language}</div>
                <div><strong>Протокол:</strong> ${info.protocol}</div>
                <div><strong>Хост:</strong> ${info.host}</div>
                <div><strong>Путь:</strong> ${info.pathname}</div>
                <div><strong>Онлайн:</strong> ${info.onLine ? 'Да' : 'Нет'}</div>
            `;

            addLog(`Система: ${info.platform}, Протокол: ${info.protocol}`);
        }

        function checkLibraryStatus() {
            const librarySection = document.getElementById('library-status');
            
            if (typeof LottiePlayer !== 'undefined') {
                librarySection.className = 'section success';
                libraryInfo.innerHTML = `
                    <div><span class="status-indicator status-ok"></span><strong>Библиотека Lottie Player:</strong> Загружена ✅</div>
                    <div><span class="status-indicator status-ok"></span><strong>Custom Elements:</strong> ${window.customElements ? 'Поддерживаются' : 'Не поддерживаются'}</div>
                    <div><span class="status-indicator status-ok"></span><strong>lottie-player element:</strong> ${window.customElements && window.customElements.get('lottie-player') ? 'Зарегистрирован' : 'Не зарегистрирован'}</div>
                `;
                addLog('Библиотека Lottie Player загружена успешно', 'success');
                return true;
            } else {
                librarySection.className = 'section error';
                libraryInfo.innerHTML = `
                    <div><span class="status-indicator status-error"></span><strong>Библиотека Lottie Player:</strong> НЕ загружена ❌</div>
                    <div><span class="status-indicator status-unknown"></span><strong>Возможные причины:</strong></div>
                    <ul>
                        <li>Проблемы с интернет-соединением</li>
                        <li>Блокировка CDN</li>
                        <li>Ошибка загрузки скрипта</li>
                    </ul>
                `;
                addLog('Библиотека Lottie Player НЕ загружена', 'error');
                return false;
            }
        }

        async function checkAnimationFiles() {
            const files = ['animations/1.json', 'animations/2.json', 'animations/3.json', 'animations/4.json'];
            const filesSection = document.getElementById('files-status');
            let allOk = true;
            let results = [];

            for (const file of files) {
                try {
                    addLog(`Проверяем файл: ${file}`);
                    const response = await fetch(file);
                    
                    if (response.ok) {
                        const data = await response.json();
                        const size = JSON.stringify(data).length;
                        results.push(`<div><span class="status-indicator status-ok"></span><strong>${file}:</strong> OK (${Math.round(size/1024)}KB)</div>`);
                        addLog(`Файл ${file} доступен (${Math.round(size/1024)}KB)`, 'success');
                    } else {
                        results.push(`<div><span class="status-indicator status-error"></span><strong>${file}:</strong> HTTP ${response.status}</div>`);
                        addLog(`Файл ${file} недоступен: HTTP ${response.status}`, 'error');
                        allOk = false;
                    }
                } catch (error) {
                    results.push(`<div><span class="status-indicator status-error"></span><strong>${file}:</strong> ${error.message}</div>`);
                    addLog(`Ошибка при проверке ${file}: ${error.message}`, 'error');
                    allOk = false;
                }
            }

            filesSection.className = allOk ? 'section success' : 'section error';
            filesInfo.innerHTML = results.join('');
        }

        function createAnimationTest(id, title, src) {
            const testDiv = document.createElement('div');
            testDiv.className = 'animation-test';
            testDiv.innerHTML = `
                <div style="text-align: center; padding: 10px; font-size: 12px; font-weight: bold;">${title}</div>
                <div id="${id}-content" style="width: 100%; height: 150px; display: flex; align-items: center; justify-content: center; color: #666;">
                    Нажмите для загрузки
                </div>
                <div style="text-align: center; padding: 5px;">
                    <button onclick="loadTestAnimation('${id}', '${src}')" style="font-size: 10px; padding: 4px 8px;">Загрузить</button>
                </div>
            `;
            return testDiv;
        }

        async function loadTestAnimation(id, src) {
            const content = document.getElementById(`${id}-content`);
            content.innerHTML = '<div style="font-size: 12px;">Загружаем...</div>';
            
            try {
                addLog(`Загружаем тестовую анимацию: ${src}`);
                
                const player = document.createElement('lottie-player');
                player.setAttribute('src', src);
                player.setAttribute('background', 'transparent');
                player.setAttribute('speed', '1');
                player.setAttribute('loop', 'true');
                player.setAttribute('autoplay', 'true');
                player.style.width = '100%';
                player.style.height = '100%';

                await new Promise((resolve, reject) => {
                    let resolved = false;
                    
                    player.addEventListener('ready', () => {
                        if (!resolved) {
                            resolved = true;
                            resolve();
                        }
                    });
                    
                    player.addEventListener('error', (e) => {
                        if (!resolved) {
                            resolved = true;
                            reject(new Error(e.detail || 'Lottie error'));
                        }
                    });
                    
                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            reject(new Error('Timeout'));
                        }
                    }, 10000);
                });

                content.innerHTML = '';
                content.appendChild(player);
                addLog(`Тестовая анимация ${id} загружена успешно`, 'success');
                
            } catch (error) {
                content.innerHTML = `<div style="font-size: 10px; color: red;">Ошибка: ${error.message}</div>`;
                addLog(`Ошибка загрузки тестовой анимации ${id}: ${error.message}`, 'error');
            }
        }

        function testOnlineAnimation() {
            testsContainer.innerHTML = '';
            const test = createAnimationTest('online', 'Онлайн тест', 'https://assets3.lottiefiles.com/packages/lf20_V9t630.json');
            testsContainer.appendChild(test);
        }

        function testLocalAnimations() {
            testsContainer.innerHTML = '';
            const files = ['animations/1.json', 'animations/2.json', 'animations/3.json', 'animations/4.json'];
            
            files.forEach((file, index) => {
                const test = createAnimationTest(`local${index + 1}`, `Локальная ${index + 1}`, file);
                testsContainer.appendChild(test);
            });
        }

        function testAllAnimations() {
            testsContainer.innerHTML = '';
            
            // Онлайн тест
            const onlineTest = createAnimationTest('online', 'Онлайн тест', 'https://assets3.lottiefiles.com/packages/lf20_V9t630.json');
            testsContainer.appendChild(onlineTest);
            
            // Локальные тесты
            const files = ['animations/1.json', 'animations/2.json', 'animations/3.json', 'animations/4.json'];
            files.forEach((file, index) => {
                const test = createAnimationTest(`local${index + 1}`, `Локальная ${index + 1}`, file);
                testsContainer.appendChild(test);
            });
        }

        function clearTests() {
            testsContainer.innerHTML = '';
        }

        // Инициализация при загрузке страницы
        window.addEventListener('load', () => {
            addLog('Страница загружена, начинаем диагностику');
            updateSystemInfo();
            
            // Проверяем библиотеку с задержкой
            setTimeout(() => {
                checkLibraryStatus();
                checkAnimationFiles();
            }, 1000);
        });

        // Обработчики ошибок
        window.addEventListener('error', (e) => {
            addLog(`JavaScript ошибка: ${e.message} в ${e.filename}:${e.lineno}`, 'error');
        });

        window.addEventListener('unhandledrejection', (e) => {
            addLog(`Необработанная ошибка промиса: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
