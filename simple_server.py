#!/usr/bin/env python3
import http.server
import socketserver
import os
import mimetypes
from datetime import datetime

PORT = 8000

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Добавляем CORS заголовки
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # Логируем запросы
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] GET {self.path}")
        
        # Если запрашивается корень, отдаем lottie-demo.html
        if self.path == '/':
            self.path = '/lottie-demo.html'
        
        # Вызываем родительский метод
        super().do_GET()
    
    def log_message(self, format, *args):
        # Переопределяем логирование для более красивого вывода
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def run_server():
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 Сервер запущен на http://localhost:{PORT}")
            print(f"📁 Корневая директория: {os.getcwd()}")
            print(f"🎬 Откройте http://localhost:{PORT} для просмотра Lottie Demo")
            print(f"🔧 Для остановки нажмите Ctrl+C")
            print("-" * 50)
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Получен сигнал прерывания, останавливаем сервер...")
        print("✅ Сервер остановлен")
    except Exception as e:
        print(f"❌ Ошибка сервера: {e}")

if __name__ == "__main__":
    run_server()
