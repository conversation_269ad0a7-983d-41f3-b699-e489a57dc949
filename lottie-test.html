<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Test - Диагностика</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .animation-container {
            width: 300px;
            height: 300px;
            border: 2px solid #ddd;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Диагностика Lottie Анимаций</h1>
        
        <div id="status-container">
            <div class="status info">Проверяем загрузку библиотеки Lottie...</div>
        </div>

        <h3>Тест 1: Простая анимация</h3>
        <div class="animation-container" id="animation1">
            <div style="text-align: center; padding-top: 130px; color: #666;">
                Загружаем анимацию...
            </div>
        </div>
        
        <div>
            <button onclick="testAnimation1()">Загрузить анимацию 1</button>
            <button onclick="testAnimation2()">Загрузить анимацию 2</button>
            <button onclick="checkLibrary()">Проверить библиотеку</button>
            <button onclick="clearLogs()">Очистить логи</button>
        </div>

        <h3>Логи диагностики:</h3>
        <div id="logs" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
    </div>

    <!-- Загружаем Lottie Player -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    
    <script>
        let logContainer = document.getElementById('logs');
        let statusContainer = document.getElementById('status-container');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logContainer.textContent += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Также показываем в статусе
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusContainer.appendChild(statusDiv);
        }

        function clearLogs() {
            logContainer.textContent = '';
            statusContainer.innerHTML = '<div class="status info">Логи очищены</div>';
        }

        function checkLibrary() {
            log('🔍 Проверяем доступность библиотеки Lottie...');
            
            if (typeof LottiePlayer !== 'undefined') {
                log('✅ LottiePlayer доступен', 'success');
            } else {
                log('❌ LottiePlayer не найден', 'error');
            }

            if (window.customElements && window.customElements.get('lottie-player')) {
                log('✅ Custom element lottie-player зарегистрирован', 'success');
            } else {
                log('❌ Custom element lottie-player не зарегистрирован', 'error');
            }

            // Проверяем доступность файлов анимаций
            checkAnimationFile('animations/1.json');
        }

        async function checkAnimationFile(filePath) {
            log(`🔍 Проверяем файл: ${filePath}`);
            try {
                const response = await fetch(filePath);
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Файл ${filePath} загружен успешно (размер: ${JSON.stringify(data).length} символов)`, 'success');
                    return data;
                } else {
                    log(`❌ Ошибка загрузки ${filePath}: ${response.status} ${response.statusText}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`❌ Ошибка при загрузке ${filePath}: ${error.message}`, 'error');
                return null;
            }
        }

        async function testAnimation1() {
            log('🎬 Тестируем анимацию 1...');
            
            const container = document.getElementById('animation1');
            container.innerHTML = '<div style="text-align: center; padding-top: 130px; color: #666;">Загружаем...</div>';

            try {
                // Проверяем файл
                const animationData = await checkAnimationFile('animations/1.json');
                if (!animationData) {
                    return;
                }

                // Создаем lottie-player элемент
                const player = document.createElement('lottie-player');
                player.setAttribute('src', 'animations/1.json');
                player.setAttribute('background', 'transparent');
                player.setAttribute('speed', '1');
                player.setAttribute('loop', 'true');
                player.setAttribute('autoplay', 'true');
                player.style.width = '100%';
                player.style.height = '100%';

                // Добавляем обработчики событий
                player.addEventListener('ready', () => {
                    log('✅ Анимация 1 готова к воспроизведению', 'success');
                });

                player.addEventListener('error', (e) => {
                    log(`❌ Ошибка в анимации 1: ${e.detail || e.message}`, 'error');
                });

                // Заменяем содержимое контейнера
                container.innerHTML = '';
                container.appendChild(player);

                log('📦 Элемент lottie-player добавлен в DOM');

            } catch (error) {
                log(`❌ Ошибка при создании анимации 1: ${error.message}`, 'error');
            }
        }

        async function testAnimation2() {
            log('🎬 Тестируем анимацию 2...');
            
            try {
                const animationData = await checkAnimationFile('animations/2.json');
                if (!animationData) {
                    return;
                }
                log('✅ Анимация 2 файл доступен', 'success');
            } catch (error) {
                log(`❌ Ошибка при тестировании анимации 2: ${error.message}`, 'error');
            }
        }

        // Проверяем библиотеку при загрузке страницы
        window.addEventListener('load', () => {
            log('🚀 Страница загружена');
            setTimeout(() => {
                checkLibrary();
            }, 1000);
        });

        // Обработчик ошибок
        window.addEventListener('error', (e) => {
            log(`❌ JavaScript ошибка: ${e.message} в ${e.filename}:${e.lineno}`, 'error');
        });

        // Обработчик для необработанных промисов
        window.addEventListener('unhandledrejection', (e) => {
            log(`❌ Необработанная ошибка промиса: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
