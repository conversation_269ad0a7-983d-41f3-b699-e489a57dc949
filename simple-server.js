const http = require('http');
const fs = require('fs');
const path = require('path');

const port = 8000;

// MIME типы для разных файлов
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

    let filePath = '.' + req.url;
    
    // Если запрашивается корень, отдаем lottie-demo.html
    if (filePath === './') {
        filePath = './lottie-demo.html';
    }

    // Получаем расширение файла
    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';

    // Читаем файл
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // Файл не найден
                console.log(`❌ Файл не найден: ${filePath}`);
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end(`
                    <h1>404 - Файл не найден</h1>
                    <p>Запрашиваемый файл <code>${req.url}</code> не найден.</p>
                    <p><a href="/">Вернуться на главную</a></p>
                `);
            } else {
                // Другая ошибка сервера
                console.log(`❌ Ошибка сервера: ${error.code}`);
                res.writeHead(500);
                res.end(`Ошибка сервера: ${error.code}`);
            }
        } else {
            // Успешно отдаем файл
            console.log(`✅ Отдаем файл: ${filePath} (${mimeType})`);
            res.writeHead(200, { 
                'Content-Type': mimeType,
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
            });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(port, () => {
    console.log(`🚀 Сервер запущен на http://localhost:${port}`);
    console.log(`📁 Корневая директория: ${process.cwd()}`);
    console.log(`🎬 Откройте http://localhost:${port} для просмотра Lottie Demo`);
    console.log(`🔧 Для остановки нажмите Ctrl+C`);
});

// Обработка сигналов для корректного завершения
process.on('SIGINT', () => {
    console.log('\n🛑 Получен сигнал SIGINT, останавливаем сервер...');
    server.close(() => {
        console.log('✅ Сервер остановлен');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Получен сигнал SIGTERM, останавливаем сервер...');
    server.close(() => {
        console.log('✅ Сервер остановлен');
        process.exit(0);
    });
});
