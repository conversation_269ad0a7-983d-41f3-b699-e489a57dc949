// Service Worker для агрессивного кеширования Lottie анимаций
const CACHE_NAME = 'lottie-demo-v2';
const CACHE_URLS = [
    './',
    './lottie-demo.html',
    'https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js'
];

// Установка Service Worker
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker устанавливается...');

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('📦 Кеширование основных ресурсов');
                // Пытаемся кешировать основные файлы
                return cache.addAll(CACHE_URLS).catch((error) => {
                    console.warn('⚠️ Не удалось предварительно кешировать некоторые файлы:', error);
                    // Продолжаем работу даже если не все файлы удалось кешировать
                });
            })
            .then(() => {
                console.log('✅ Service Worker установлен');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.warn('⚠️ Ошибка при установке Service Worker:', error);
                return self.skipWaiting();
            })
    );
});

// Активация Service Worker
self.addEventListener('activate', (event) => {
    console.log('🚀 Service Worker активируется...');
    
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('🗑️ Удаляем старый кеш:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('✅ Service Worker активирован');
            return self.clients.claim();
        })
    );
});

// Перехват запросов
self.addEventListener('fetch', (event) => {
    const url = event.request.url;
    
    // Агрессивное кеширование для JSON файлов анимаций
    if (url.includes('.json') || url.includes('animations/')) {
        event.respondWith(
            caches.open(CACHE_NAME).then((cache) => {
                return cache.match(event.request).then((cachedResponse) => {
                    if (cachedResponse) {
                        console.log('📦 Загружаем из кеша:', url);
                        return cachedResponse;
                    }
                    
                    console.log('🌐 Загружаем из сети и кешируем:', url);
                    return fetch(event.request).then((response) => {
                        // Кешируем только успешные ответы
                        if (response.status === 200) {
                            cache.put(event.request, response.clone());
                        }
                        return response;
                    });
                });
            })
        );
        return;
    }
    
    // Для остальных ресурсов - стандартная стратегия
    event.respondWith(
        caches.match(event.request).then((response) => {
            return response || fetch(event.request);
        })
    );
});

// Предзагрузка анимаций при получении сообщения
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'PRELOAD_ANIMATIONS') {
        const animationUrls = event.data.urls;
        
        console.log('🎬 Предзагружаем анимации в кеш:', animationUrls);
        
        event.waitUntil(
            caches.open(CACHE_NAME).then((cache) => {
                return Promise.all(
                    animationUrls.map((url) => {
                        return fetch(url).then((response) => {
                            if (response.status === 200) {
                                return cache.put(url, response);
                            }
                        }).catch((error) => {
                            console.warn('⚠️ Не удалось предзагрузить:', url, error);
                        });
                    })
                );
            }).then(() => {
                console.log('✅ Все анимации предзагружены в кеш');
            })
        );
    }
});
