<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Minimal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .animation-box {
            width: 300px;
            height: 300px;
            border: 2px solid #ddd;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #e7f3ff;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Минимальный тест Lottie</h1>
        
        <div class="status" id="status">
            Проверяем библиотеку...
        </div>

        <h3>Тест 1: Простая анимация</h3>
        <div class="animation-box" id="test1">
            <div style="text-align: center; padding-top: 130px; color: #666;">
                Нажмите кнопку для загрузки
            </div>
        </div>
        <button onclick="testSimpleAnimation()">Загрузить анимацию 1</button>

        <h3>Консоль:</h3>
        <div class="log" id="console"></div>
    </div>

    <!-- Загружаем Lottie Player -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    
    <script>
        const statusEl = document.getElementById('status');
        const consoleEl = document.getElementById('console');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleEl.textContent += `[${timestamp}] ${message}\n`;
            consoleEl.scrollTop = consoleEl.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, isError = false) {
            statusEl.textContent = message;
            statusEl.style.background = isError ? '#ffebee' : '#e7f3ff';
            log(message);
        }

        // Проверяем библиотеку
        function checkLibrary() {
            log('🔍 Проверяем библиотеку Lottie...');
            log(`typeof LottiePlayer: ${typeof LottiePlayer}`);
            log(`window.customElements: ${!!window.customElements}`);
            
            if (window.customElements) {
                log(`lottie-player registered: ${!!window.customElements.get('lottie-player')}`);
            }
            
            if (typeof LottiePlayer !== 'undefined') {
                updateStatus('✅ Библиотека Lottie загружена');
                return true;
            } else {
                updateStatus('❌ Библиотека Lottie НЕ загружена', true);
                return false;
            }
        }

        // Простой тест анимации
        async function testSimpleAnimation() {
            log('🎬 Начинаем тест простой анимации...');
            
            if (!checkLibrary()) {
                log('❌ Библиотека не доступна, тест прерван');
                return;
            }

            const container = document.getElementById('test1');
            container.innerHTML = '<div style="text-align: center; padding-top: 130px;">Загружаем...</div>';

            try {
                // Проверяем файл
                log('🔍 Проверяем файл animations/1.json...');
                const response = await fetch('animations/1.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                log('✅ Файл animations/1.json доступен');

                // Создаем элемент
                log('🔧 Создаем lottie-player элемент...');
                const player = document.createElement('lottie-player');
                
                // Устанавливаем атрибуты
                player.setAttribute('src', 'animations/1.json');
                player.setAttribute('background', 'transparent');
                player.setAttribute('speed', '1');
                player.setAttribute('loop', 'true');
                player.setAttribute('autoplay', 'true');
                player.style.width = '100%';
                player.style.height = '100%';

                log('🔧 Атрибуты установлены, добавляем в DOM...');

                // Добавляем обработчики событий
                player.addEventListener('ready', () => {
                    log('✅ Событие ready получено - анимация готова!');
                    updateStatus('✅ Анимация загружена успешно!');
                });

                player.addEventListener('error', (e) => {
                    log(`❌ Событие error: ${e.detail || e.message}`);
                    updateStatus('❌ Ошибка загрузки анимации', true);
                });

                player.addEventListener('load', () => {
                    log('✅ Событие load получено');
                });

                // Добавляем в DOM
                container.innerHTML = '';
                container.appendChild(player);
                log('✅ Элемент добавлен в DOM');

                // Ждем немного и проверяем состояние
                setTimeout(() => {
                    log(`🔍 Проверяем состояние через 2 секунды:`);
                    log(`- player.tagName: ${player.tagName}`);
                    log(`- player.src: ${player.src}`);
                    log(`- player.loop: ${player.loop}`);
                    log(`- player.autoplay: ${player.autoplay}`);
                }, 2000);

            } catch (error) {
                log(`❌ Ошибка: ${error.message}`);
                updateStatus(`❌ Ошибка: ${error.message}`, true);
                container.innerHTML = `<div style="text-align: center; padding-top: 130px; color: red;">Ошибка: ${error.message}</div>`;
            }
        }

        // Инициализация
        window.addEventListener('load', () => {
            log('🚀 Страница загружена');
            setTimeout(() => {
                checkLibrary();
            }, 1000);
        });

        // Обработчик ошибок
        window.addEventListener('error', (e) => {
            log(`❌ JavaScript ошибка: ${e.message}`);
        });
    </script>
</body>
</html>
