<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lot<PERSON> Demo - Рабочая версия</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .animations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .animation-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .animation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .animation-container {
            width: 100%;
            height: 300px;
            border-radius: 15px;
            overflow: hidden;
            background: #f8f9fa;
            position: relative;
            margin-bottom: 20px;
        }

        .animation-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            color: #999;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .animation-placeholder:hover {
            background: linear-gradient(45deg, #e0e0e0, #d0d0d0);
        }

        .animation-placeholder.loading {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .animation-info {
            text-align: center;
        }

        .animation-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .animation-description {
            color: #666;
            font-size: 0.95rem;
            margin-bottom: 15px;
        }

        .animation-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        lottie-player {
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        lottie-player.loaded {
            opacity: 1;
        }

        .debug-info {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎬 Lottie Animations Demo</h1>
            <p>Рабочая версия с диагностикой</p>
            
            <div class="debug-info" id="debug-info">
                <h3>🔍 Диагностика:</h3>
                <div id="debug-status">Проверяем...</div>
            </div>
        </header>

        <div class="animations-grid" id="animationsGrid">
            <!-- Анимации будут добавлены через JavaScript -->
        </div>
    </div>

    <!-- Загружаем Lottie Player с CDN -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    
    <script>
        const debugStatus = document.getElementById('debug-status');
        
        function updateDebug(message) {
            debugStatus.innerHTML += `<div>• ${message}</div>`;
            console.log(message);
        }

        const animations = [
            {
                id: 'animation1',
                title: 'Анимация 1',
                description: 'Первая тестовая анимация',
                file: 'animations/1.json'
            },
            {
                id: 'animation2',
                title: 'Анимация 2',
                description: 'Вторая тестовая анимация',
                file: 'animations/2.json'
            },
            {
                id: 'animation3',
                title: 'Анимация 3',
                description: 'Третья тестовая анимация',
                file: 'animations/3.json'
            },
            {
                id: 'animation4',
                title: 'Анимация 4',
                description: 'Четвертая тестовая анимация',
                file: 'animations/4.json'
            }
        ];

        function createAnimationCard(animation) {
            updateDebug(`Создаем карточку: ${animation.title}`);
            
            const card = document.createElement('div');
            card.className = 'animation-card';
            card.id = animation.id;

            card.innerHTML = `
                <div class="animation-container" data-animation="${animation.file}">
                    <div class="animation-placeholder" onclick="loadAnimation('${animation.id}', '${animation.file}')">
                        <span>👁️ Нажмите для загрузки</span>
                    </div>
                </div>
                <div class="animation-info">
                    <div class="animation-title">${animation.title}</div>
                    <div class="animation-description">${animation.description}</div>
                    <div class="animation-controls">
                        <button class="btn btn-primary" onclick="playAnimation('${animation.id}')">▶️ Играть</button>
                        <button class="btn btn-secondary" onclick="pauseAnimation('${animation.id}')">⏸️ Пауза</button>
                        <button class="btn btn-secondary" onclick="restartAnimation('${animation.id}')">🔄 Сначала</button>
                    </div>
                </div>
            `;
            
            return card;
        }

        function createAnimationCards() {
            updateDebug('Начинаем создание карточек анимаций');
            const grid = document.getElementById('animationsGrid');
            
            if (!grid) {
                updateDebug('❌ ОШИБКА: Контейнер animationsGrid не найден!');
                return;
            }
            
            animations.forEach(animation => {
                const card = createAnimationCard(animation);
                grid.appendChild(card);
            });
            
            updateDebug(`✅ Создано ${animations.length} карточек анимаций`);
        }

        async function loadAnimation(animationId, animationFile) {
            updateDebug(`Загружаем анимацию: ${animationId} (${animationFile})`);
            
            const container = document.querySelector(`#${animationId} .animation-container`);
            const placeholder = container.querySelector('.animation-placeholder');
            
            // Показываем загрузку
            placeholder.className = 'animation-placeholder loading';
            placeholder.innerHTML = '<div class="loading-spinner"></div><span>Загружаем...</span>';

            try {
                // Проверяем библиотеку Lottie
                if (typeof LottiePlayer === 'undefined') {
                    throw new Error('Библиотека Lottie Player не загружена');
                }

                // Создаем lottie-player
                const player = document.createElement('lottie-player');
                player.setAttribute('src', animationFile);
                player.setAttribute('background', 'transparent');
                player.setAttribute('speed', '1');
                player.setAttribute('loop', 'true');
                player.setAttribute('autoplay', 'true');
                
                // Ждем загрузки
                await new Promise((resolve, reject) => {
                    let resolved = false;
                    
                    player.addEventListener('ready', () => {
                        if (!resolved) {
                            resolved = true;
                            resolve();
                        }
                    });
                    
                    player.addEventListener('error', (e) => {
                        if (!resolved) {
                            resolved = true;
                            reject(new Error(`Lottie error: ${e.detail || e.message}`));
                        }
                    });
                    
                    // Таймаут
                    setTimeout(() => {
                        if (!resolved) {
                            resolved = true;
                            reject(new Error('Timeout'));
                        }
                    }, 10000);
                });

                // Заменяем placeholder на player
                container.innerHTML = '';
                container.appendChild(player);
                
                // Плавное появление
                setTimeout(() => {
                    player.classList.add('loaded');
                }, 100);

                updateDebug(`✅ Анимация ${animationId} загружена успешно`);

            } catch (error) {
                updateDebug(`❌ Ошибка загрузки ${animationId}: ${error.message}`);
                placeholder.className = 'animation-placeholder';
                placeholder.innerHTML = `❌ Ошибка: ${error.message}<br><button onclick="loadAnimation('${animationId}', '${animationFile}')" style="margin-top: 10px; padding: 5px 10px; border: none; background: #007bff; color: white; border-radius: 3px; cursor: pointer;">🔄 Повторить</button>`;
            }
        }

        function playAnimation(animationId) {
            const player = document.querySelector(`#${animationId} lottie-player`);
            if (player) player.play();
        }

        function pauseAnimation(animationId) {
            const player = document.querySelector(`#${animationId} lottie-player`);
            if (player) player.pause();
        }

        function restartAnimation(animationId) {
            const player = document.querySelector(`#${animationId} lottie-player`);
            if (player) {
                player.stop();
                player.play();
            }
        }

        // Инициализация
        document.addEventListener('DOMContentLoaded', () => {
            updateDebug('🚀 DOM загружен');
            updateDebug(`Протокол: ${window.location.protocol}`);
            updateDebug(`Хост: ${window.location.host}`);
            
            // Проверяем библиотеку Lottie
            setTimeout(() => {
                if (typeof LottiePlayer !== 'undefined') {
                    updateDebug('✅ Библиотека Lottie Player загружена');
                } else {
                    updateDebug('❌ Библиотека Lottie Player НЕ загружена');
                }
                
                // Создаем карточки
                createAnimationCards();
            }, 1000);
        });

        // Обработчики ошибок
        window.addEventListener('error', (e) => {
            updateDebug(`❌ JavaScript ошибка: ${e.message}`);
        });
    </script>
</body>
</html>
